name: Deploy Smart Eye API To Production (Two Droplets)

on:
    push:
        branches: [master] # deploy only when code is pushed/merged to master

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v2

            # Deploy to first droplet
            - name: Deploy to Droplet 1

              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.SSH_HOST_1 }}
                  username: ${{ secrets.SSH_USERNAME_1 }}
                  key: ${{ secrets.SSH_PRIVATE_KEY_1 }}
                  script: |
                      cd /var/www/api.smarteye.smartflowtech.com
                      git fetch origin master
                      git reset --hard origin/master
                      source venv/bin/activate
                      python -m pip install --upgrade pip
                      if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
                      python manage.py migrate --noinput
                      deactivate
                      sudo systemctl daemon-reload
                      sudo systemctl restart smarteyeapi

            # Deploy to second droplet
            - name: Deploy to Droplet 2
              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.SSH_HOST_2 }}
                  username: ${{ secrets.SSH_USERNAME_2 }}
                  key: ${{ secrets.SSH_PRIVATE_KEY_2 }}
                  script: |
                      cd /var/www/api.smarteye.smartflowtech.com
                      git fetch origin master
                      git reset --hard origin/master
                      source venv/bin/activate
                      python -m pip install --upgrade pip
                      if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
                      python manage.py migrate --noinput
                      deactivate
                      sudo systemctl daemon-reload
                      sudo systemctl restart smarteyeapi
