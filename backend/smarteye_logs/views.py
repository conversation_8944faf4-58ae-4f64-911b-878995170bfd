from ast import Pass
from collections import OrderedDict
from itertools import count
import json
import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib import request
import requests
import MySQLdb as mdb
from backend.file_handler import get_tank_calibration_chart
from decouple import config
# from channels.layers import get_channel_layer
# from asgiref.sync import async_to_sync
# from backend.tanks.consumers import push_tank_update
from . import serializer
from django.db import IntegrityError
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from backend.smarteye_logs import serializer as log_serializer
import threading
from django.db import connection
from django.shortcuts import get_object_or_404
from .. import permissions
from rest_framework import status  # type: ignore
from rest_framework import generics  # type: ignore
from rest_framework.views import APIView  # type: ignore
from rest_framework.renderers import JSONRenderer  # type: ignore
from django.http import HttpResponse, JsonResponse  # type: ignore
from rest_framework.parsers import J<PERSON>NParser  # type: ignore
from rest_framework.response import Response  # type: ignore

from backend import models
from backend.companies import serializer
from backend.custom_pagination import SQLHeaderLimitOffsetPagination, PageNumberHeaderPagination
from backend.tasks import tank_alarm_task, analog_probe_logger, pic_tank_alarm_task
from ..tanks.serializer import TankSerializer
from . utils import determine_pv, to_liters
from . queries import get_latest_tank_log, update_pic_latest_atg_log, update_device_online_status_for_PicAtg, update_pic_tls_latest_atg_log, get_dashboard
from .. import utils
from .. import sql_helpers
from .. import tank_calibration
from .serializer import LogSerializer, TankLogAnomalySerializer, TankMapSerializer, LatestTankLogSerializer
from . import utils as log_utils
from . import queries as q
from decouple import config
from django.db import connections
from django.db.utils import OperationalError, ConnectionDoesNotExist
from django.db.models import F, OuterRef, Subquery, IntegerField
from django.db.models.functions import Cast
from dateutil import parser

'''
VIEWS
- Regular data logger
- Sensor data logger
- Tank readings
- Tankgroup readings
- Latest tank reading
- Latest tankgroup reading
'''


class DataLogger(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        log_data = request.data
        saved_log = 0
        duplicate_found = 0

        for items in range(len(log_data)):
            if len(log_data[items]) == 14:
                duplicate_found = 0
                count_log_fail = 0
                error = "No errors"
                index = 0
                log_lenght = len(log_data)

                for each in log_data:
                    check_log_exist = models.AtgPrimaryLog.objects.filter(
                        local_id=each[0], device_address=each[5], read_at=each[1])
                    tank_id = q.get_tank_id(each)
                    q.update_device_online_status(each[5])

                    if check_log_exist:
                        duplicate_found += 1
                        continue

                    if each[2] is None:
                        each[2], each[4], message = log_utils.compute_pv(
                            each[5], each[4], each[7])
                        if message != "Done":
                            count_log_fail += 1
                            each.append(False)
                            error = message
                        else:
                            each.append(True)
                    else:
                        each.append(True)

                try:
                    logs = [
                        models.AtgPrimaryLog(
                            local_id=d[0], read_at=d[1], pv=d[2], pv_flag=d[3], sv=d[4],
                            device_address=d[5], multicont_polling_address=d[6],
                            tank_index=d[7], tc_volume=d[8], water=d[9], temperature=d[10],
                            controller_type=d[11], status=d[12], probe_address=d[13],
                            flag_log=d[14], tank_id=tank_id  # type: ignore
                        ) for d in log_data
                    ]
                except IndexError:
                    logs = [
                        models.AtgPrimaryLog(
                            local_id=d[0], read_at=d[1], pv=d[2], pv_flag=d[3], sv=d[4],
                            device_address=d[5], multicont_polling_address=d[6],
                            tank_index=d[7], tc_volume=d[8], water=d[9], temperature=d[10],

                            # type: ignore
                            controller_type=d[11], status=d[12], tank_id=tank_id
                        ) for d in log_data
                    ]

                route_mac_addresses = [
                    'b8:27:eb:d9:ea:ea', 'b8:27:eb:2c:06:b4']
                log_mac_address = log_data[0][5]

                if log_mac_address in route_mac_addresses:
                    try:
                        conn = mdb.connect(
                            config("SM_DB_HOST"),
                            config("SM_DB_USER"),
                            config("SM_DB_PASSWORD"),
                            config("SM_DB_NAME")
                        )
                        with conn:
                            cur = conn.cursor()
                            query = "INSERT INTO atg_primary_log (local_id, read_at, pv, pv_flag, sv, device_address, multicont_polling_address, tank_index, tc_volume, water, temperature, controller_type ) VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                            cur.executemany(query, log_data)
                            count_log_fail = 0

                    except mdb.Error:
                        pass
                else:
                    try:
                        models.AtgPrimaryLog.objects.bulk_create(logs)
                    except IntegrityError as e:
                        duplicate_found += 1
                        # Optional: log the error
                        print("IntegrityError during bulk_create:", e)

                filtered_logs = log_utils.filter_for_latest_logs(log_data)
                q.upadate_latest_atg_log(log_data)
                tank_alarm_task.delay(filtered_logs)
                saved_log += 1

                # # 🔥 Push WebSocket update
                # channel_layer = get_channel_layer()
                # payload = {
                #     "event": "tank_log_update",
                #     "saved_count": saved_log,
                #     "duplicate_count": duplicate_found,
                #     "latest_logs": filtered_logs
                # }
                # async_to_sync(channel_layer.group_send)(
                #     "updates",
                #     {
                #         "type": "send_update",
                #         "message": payload
                #     }
                # )
                return utils.UpdatedCustomResponse.Success(
                    f"{duplicate_found} Duplicate logs(s) found, {saved_log} log(s) saved successfully",
                    status=status.HTTP_200_OK
                )
            else:
                count_log_fail = 0
                duplicate_found = 0
                log_lenght = len(log_data)

                for each in range(log_lenght):
                    check = models.AtgPrimaryLog.objects.filter(
                        local_id=log_data[each][1],
                        device_address=log_data[each][6],
                        read_at=log_data[each][2],
                        transaction_id=log_data[each][0]
                    ).exists()

                    if check:
                        duplicate_found += 1
                        continue

                    try:
                        models.AtgPrimaryLog.objects.create(
                            transaction_id=log_data[each][0],
                            local_id=log_data[each][1],
                            read_at=log_data[each][2],
                            pv=log_data[each][3],
                            pv_flag=log_data[each][4],
                            sv=log_data[each][5],
                            device_address=log_data[each][6],
                            multicont_polling_address=log_data[each][7],
                            tank_index=log_data[each][8],
                            tc_volume=log_data[each][9],
                            water=log_data[each][10],
                            temperature=log_data[each][11],
                            controller_type=log_data[each][12],
                            status=log_data[each][13],
                            probe_address=log_data[each][14],
                            tank_id=log_data[each][15],
                        )

                        q.upadate_latest_atg_log1(log_data)
                        q.update_device_online_status(log_data[each][6])
                        saved_log += 1
                    except IntegrityError as e:
                        duplicate_found += 1
                        print("IntegrityError during single create:", e)

                # # 🔥 Push WebSocket update
                # channel_layer = get_channel_layer()
                # payload = {
                #     "event": "tank_log_update",
                #     # "site_id": tank_id,   # or site_id if you prefer
                #     "saved_count": saved_log,
                #     "duplicate_count": duplicate_found,
                #     "latest_logs": log_data  # optional, you can send raw latest data
                # }

                # # Send to group "updates"
                # async_to_sync(channel_layer.group_send)(
                #     "updates",
                #     {
                #         "type": "send_update",  # must match consumer method
                #         "message": payload
                #     }
                # )
                return utils.UpdatedCustomResponse.Success(
                    f"{duplicate_found} Duplicate logs(s) found, {saved_log} log(s) saved successfully",
                    status=status.HTTP_200_OK
                )

# PIC Data Logger


class PicDataLogger(APIView):
    # request data formart
    ''''
    data[0]: device_address
    data[1]: multicont_polling_address
    data[2]: tank_index
    data[3]: read_at
    data[4]: pv
    data[5]: sv
    data[6]: controller_type
    data[7]: tank_id
    data[8]: transaction_id 
    '''
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):

        request_data = request.data
        duplicate_log = 0
        saved_log = 0
        # loop through request data for multiple logs
        for log in request_data:
            # check if log exis
            if models.AtgPrimaryLog.objects.filter(device_address=log[0], read_at=log[3], transaction_id=log[8]).exists():
                duplicate_log += 1
                pass
            else:
                # determine pv flag
                pv = determine_pv(log[4], log[7])
                # save log to table
                models.AtgPrimaryLog.objects.create(
                    device_address=log[0], multicont_polling_address=log[1], tank_index=log[2],
                    read_at=log[3], pv=log[4], sv=log[5], controller_type=log[6], tank_id=log[7],
                    transaction_id=log[8], pv_flag=pv
                )
                # update latest Atglog table
                update_pic_latest_atg_log(log)
                # update device online status
                update_device_online_status_for_PicAtg(log)
                # dispatch logs asynchronously to celery task for processing
                pic_tank_alarm_task.delay(log)
                saved_log += 1
            return utils.CustomResponse.Success(f"{duplicate_log} Duplicate logs(s) found, {saved_log} log(s) saved successfully", status=status.HTTP_200_OK)


class PicDataLoggerConfirm(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        transaction_id = request.query_params['id']
        if models.AtgPrimaryLog.objects.filter(transaction_id=transaction_id).exists():
            return utils.CustomResponse.Success('y', status=status.HTTP_200_OK)
        return utils.CustomResponse.Success('n', status=status.HTTP_200_OK)


class DeliveryLogger(APIView):
    '''
    TLS devices attempt to store multiple deliveries to the DB.
    '''
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        # logs are tuple of tuples; format direct from device sqlite cursor
        log_data = request.data
        # create log instances for each log in request data
        # (local_id, read_at, device_address, polling_address, tank_index,
        # volume, tc_volume, system_start_time, system_end_time,start_height,
        # end_height, start_volume, end_volume, controller_type)
        logs = [models.Deliveries(local_id=d[0], read_at=d[1],
                                  device_address=d[2], polling_address=d[3], tank_index=d[4],
                                  volume=d[5], tc_volume=d[6], system_start_time=d[7], system_end_time=d[8],
                                  start_height=d[9], end_height=d[10], start_volume=d[11], end_volume=d[12],
                                  controller_type=d[13]) for d in log_data]

        # use bulk_create to create multiple new log objects
        models.Deliveries.objects.bulk_create(logs)
        return utils.CustomResponse.Success("Done")


class SmarthubLogsLogger(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        log_data = request.data
        try:
            conn = mdb.connect(
                config("SM_DB_HOST"),
                config("SM_DB_USER"),
                config("SM_DB_PASSWORD"),
                config("SM_DB_NAME")
            )
            with conn:
                cur = conn.cursor()
                query = "INSERT INTO atg_primary_log (local_id, read_at, pv,pv_flag, sv, device_address, multicont_polling_address, tank_index, tc_volume, water, temperature, controller_type ) VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                cur.executemany(query, log_data)
            return utils.CustomResponse.Success("Insertion into db successful")
        except mdb.Error:
            return utils.CustomResponse.Failure("Error inserting logs to db")


class SmarthubDeliveriesLogger(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        log_data = request.data
        try:
            conn = mdb.connect(
                config("SM_DB_HOST"),
                config("SM_DB_USER"),
                config("SM_DB_PASSWORD"),
                config("SM_DB_NAME")
            )
            with conn:
                cur = conn.cursor()
                query = "INSERT INTO deliveries (local_id, read_at, device_address, polling_address, tank_index, volume, tc_volume, system_start_time, system_end_time, start_height, end_height, start_volume, end_volume, controller_type) VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                cur.executemany(query, log_data)
            return utils.CustomResponse.Success("Insertion into db successful")
        except mdb.Error:
            return utils.CustomResponse.Failure("Error inserting logs to db")


class SensorDataLogger(APIView):
    '''
    sensor_data is in the format
    (local_id,controller_address,tank_index,current,voltage,device_address,controller_type,read_at)
    '''
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        data = request.data
        tank = analog_probe_logger(data)
        if tank:
            return utils.CustomResponse.Success('Done')
        else:
            return utils.CustomResponse.Failure('Failed: No tank was found for the log')


class SpecificTankReading(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, *args, **kwargs):
        tank_id = self.kwargs['pk']
        data = q.get_specific_tank_reading(tank_id)
        data = log_utils.update_tank_records(data)
        return utils.CustomResponse.Success(data)


class TankReadings(generics.CreateAPIView, log_utils.SqlLogsPaginationCacheMixin):
    permission_classes = ()
    authentication_classes = ()
    pagination_class = SQLHeaderLimitOffsetPagination

    def post(self, request, *args, **kwargs):
        if (None not in request.data['site']):
            self.ids = request.data['site']
        else:
            self.ids = []
            values = models.Sites.objects.filter(
                Company__in=request.data['Company_ids']).values_list(flat=True)
            for each in values:
                self.ids.append(each)
        self.tank_ids = [each['Tank_id']
                         for each in request.data.get('tanks', None)]
        if len(self.tank_ids) == 0:
            return utils.CustomResponse.Failure('No tank is passed')
        self.start_date = request.data.get('start')
        self.end_date = request.data.get('end')
        kwargs['count_query'] = q.get_tanklogs_count
        kwargs['data_query'] = q.get_tanklogs
        return self.paginate_and_cache(request, *args, **kwargs)


class TankReadings(generics.GenericAPIView, log_utils.SqlLogsPaginationCacheMixin2):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.TankPermission)]
    pagination_class = SQLHeaderLimitOffsetPagination

    def get(self, request, *args, **kwargs):
        site_id = request.query_params.get('site')
        company_id = request.query_params.get('Company_ids')
        tanks = request.query_params.get('tanks')
        start = request.query_params.get('start')
        end = request.query_params.get('end')
        if tanks == None:
            return utils.CustomResponse.Failure(error="No Tank Passed")

        self.ids = [site_id]
        self.tank_ids = [tanks]
        self.start_date = start
        self.end_date = end
        try:
            models.Sites.objects.get(Site_id=site_id)
            kwargs['data_query'] = q.get_tanklogs
            kwargs['count_query'] = q.get_tanklogs_count
            return self.paginate_and_cache2(request, *args, **kwargs)
        except models.Sites.DoesNotExist:
            return utils.CustomResponse.Failure(error="Site Not Found", status=404)


@method_decorator(cache_page(60 * 2), name='dispatch')
class TankReadingsNewApproach(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, *args, **kwargs):
        site_id = request.query_params.get('site', '')
        tanks = request.query_params.get('tanks', '')
        start = request.query_params.get('start', '')
        end = request.query_params.get('end', '')

        if not tanks:
            return utils.CustomResponse.Failure(error="No Tank Passed")

        self.ids = [site_id]
        self.tank_ids = [tanks]
        self.start_date = start
        self.end_date = end

        try:
            connections["sqlite_db"].ensure_connection()

            if not models.Sites.objects.using("sqlite_db").filter(Site_id=site_id).exists():
                return utils.CustomResponse.Failure(error="Site Not Found", status=status.HTTP_404_NOT_FOUND)

            if not models.Tanks.objects.using("sqlite_db").filter(Tank_id=tanks).exists():
                return utils.CustomResponse.Failure(error="Tank Not Found", status=status.HTTP_404_NOT_FOUND)

            tank_name_subquery = models.Tanks.objects.using("sqlite_db").filter(
                Tank_id=Cast(OuterRef('tank_id'), IntegerField())
            ).values('Name')[:1]

            tank_display_unit_subquery = models.Tanks.objects.using("sqlite_db").filter(
                Tank_id=Cast(OuterRef('tank_id'), IntegerField())
            ).values('Display_unit')[:1]

            queryset = models.AtgPrimaryLog.objects.using("sqlite_db").filter(
                tank_id=tanks,
                read_at__range=(start, end)
            ).annotate(volume=F('pv'), height=F('sv'), tank_name=Subquery(tank_name_subquery), display_unit=Subquery(tank_display_unit_subquery)).values(
                'tank_id', 'tank_name', 'volume', 'height', 'read_at', 'display_unit', 'read_at'
            ) or []

        except (OperationalError, ConnectionDoesNotExist):

            if not models.Sites.objects.using("default").filter(Site_id=site_id).exists():
                return utils.CustomResponse.Failure(error="Site Not Found", status=status.HTTP_404_NOT_FOUND)

            if not models.Tanks.objects.using("default").filter(Tank_id=tanks).exists():
                return utils.CustomResponse.Failure(error="Tank Not Found", status=status.HTTP_404_NOT_FOUND)

            tank_name_subquery = models.Tanks.objects.using("default").filter(
                Tank_id=Cast(OuterRef('tank_id'), IntegerField())
            ).values('Name')[:1]

            tank_display_unit_subquery = models.Tanks.objects.using("default").filter(
                Tank_id=Cast(OuterRef('tank_id'), IntegerField())
            ).values('Display_unit')[:1]

            queryset = models.AtgPrimaryLog.objects.using("default").filter(
                tank_id=tanks,
                read_at__range=(start, end)
            ).annotate(volume=F('pv'), height=F('sv'), tank_name=Subquery(tank_name_subquery), display_unit=Subquery(tank_display_unit_subquery)).values(
                'tank_id', 'tank_name', 'volume', 'height', 'read_at', 'display_unit', 'read_at'
            ) or []

        if not queryset:
            return utils.CustomResponse.Success(data=[], status=status.HTTP_200_OK)
        return utils.CustomResponse.Success(data=queryset, status=status.HTTP_200_OK)


# class TankReadings(generics.CreateAPIView, log_utils.SqlLogsPaginationCacheMixin):
#     permission_classes = ()
#     authentication_classes = ()
#     pagination_class = SQLHeaderLimitOffsetPagination
#     serializer_class = LatestTankLogSerializer

#     def get(self, request, *args, **kwargs):

#         print(request.query_params)
#         site_id = request.query_params.get('site')
#         company_id = request.query_params.get('Company_ids')
#         tanks = request.query_params.get('tanks')
#         start = request.query_params.get('start')
#         end = request.query_params.get('end')

#         print(site_id, company_id, tanks, start, end)
#         # if (None not in request.data['site']):
#         if (site_id):
#             # self.ids = request.data['site']
#             self.ids = site_id
#         else:
#             self.ids = []
#             values = models.Sites.objects.filter(
#                 # Company__in=request.data['Company_ids']).values_list(flat=True)
#                 Company__in=company_id).values_list(flat=True)
#             for each in values:
#                 self.ids.append(each)
#         self.tank_ids = tanks
#         # self.tank_ids = [each['Tank_id']
#         #                  for each in request.data.get('tanks', None)]
#         if len(self.tank_ids) == 0:
#             return utils.CustomResponse.Failure('No tank is passed')
#         self.start_date = start
#         self.end_date =end
#         # self.start_date = request.data.get('start')
#         # self.end_date = request.data.get('end')
#         kwargs['count_query'] = q.get_tanklogs_count(site_id, tanks,start, end,100000,0)
#         kwargs['data_query'] = q.get_tanklogs(site_id, tanks,start, end,100000,0)
#         # kwargs['count_query'] = q.get_tanklogs_count
#         # kwargs['data_query'] = q.get_tanklogs
#         return self.paginate_and_cache(request, *args, **kwargs)


class AnomalyTankReadingReport(TankReadings):
    pagination_class = SQLHeaderLimitOffsetPagination
    serializer = TankLogAnomalySerializer()

    def get(self, request):
        """
        List all tank logs, or save an anomaly log.
        """

        return HttpResponse()

        # req_param ={
        #     "Company_ids":	[1],
        #     "end":	"2022-08-11 09:36",
        #     "site":	[137],
        #     "start":	"2022-07-18 09:36",
        #     "tanks":	[
        #     {
        #         "Name":	"PMS TANK 1",
        #     "Tank_id":	336
        # }
        # ]
        # }
        # self.ids = req_param['Company_ids']
        # self.tank_ids = req_param['tanks']
        # self.start_date = req_param['start']
        # self.end_date = req_param['end']
        # kwargs['count_query'] = q.get_tanklogs_count
        # kwargs['data_query'] = q.get_tanklogs
        # time_logs = self.paginate_and_cache(request, *args, **kwargs)
        # for time_stamp in range(len(time_logs)):
        #         prev_time = time_logs[time_stamp - 1]
        #         current_time = time_logs[time_stamp]
        #         t1 = datetime.datetime.strptime(prev_time, "%Y-%m-%d %H:%M:%S").minute
        #         t2 = datetime.datetime.strptime(current_time, "%Y-%m-%d %H:%M:%S").minute
        #         td = datetime.datetime.strptime('2022-06-22 09:46:04', "%Y-%m-%d %H:%M:%S")
        #         time_delta = (t2) - (t1)
        #         if abs(time_delta) > 10:
        #             print(f"anomaly occured by {abs(time_delta)} minutes")
        # print(f"{t1} - {t2}")
        # print(abs(time_delta))

        # atg_log = models.AtgPrimaryLog.objects.all()[:100]
        # log = LogSerializer(atg_log, many=True).data
        # dict = list(log)
        # time_logs = []

        # current_time_log = i['read_at']
        # if current_time_log is not None:
        #     prev_time_log = current_time_log
        # print(current_time_log, "curr")

        # return JsonResponse(status=200)
        #     return JsonResponse(serializer.errors, status=400)


class RevampedTankReadings(generics.CreateAPIView, log_utils.RevampedSqlLogsPaginationCacheMixin):
    # pagination_class = SQLHeaderLimitOffsetPagination
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        self.tank_ids = [each['Tank_id']
                         for each in request.data.get('tanks', None)]
        self.start_date = request.data.get('start')
        self.end_date = request.data.get('end')
        kwargs['data_query'] = q.revamped_get_tanklogs
        return self.paginate_and_cache(request, *args, **kwargs)


class TankReadingsForTankGroups(generics.CreateAPIView, log_utils.SqlLogsPaginationCacheMixin):
    # pagination_class = SQLHeaderLimitOffsetPagination
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        self.ids = request.data.get('tankgroup', None)
        self.start_date = request.data.get('start')
        self.end_date = request.data.get('end')
        kwargs['count_query'] = q.get_tankgrouplogs_count
        kwargs['data_query'] = q.get_tankgroup_logs
        return self.paginate_and_cache(request, *args, **kwargs)


class TankLogAnomalyView(generics.CreateAPIView, log_utils.SqlLogsPaginationCacheMixin):
    Pass


class ModifiedCurrentTankDetails(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        site_id = request.data.get('site', None)
        if site_id:
            # get tank_details from the sites required
            site_device = models.Sites.objects.get(Site_id=site_id)

            try:
                mac_address = site_device.Device.Device_unique_address
            except:
                return utils.CustomResponse.Success([])

            tank_details = list(models.Tanks.objects.filter(Site=site_id, Status=True).values(
                'Name', 'Product', 'Tank_index', 'Controller_polling_address', 'Tank_controller', 'Capacity', 'Display_unit', 'UOM'))

            data = []
            # use threading to get the logs for each tank
            with ThreadPoolExecutor() as executor:
                tank_details_futures = [executor.submit(
                    q.modified_get_tank_latest_log,
                    tank['Tank_controller'],
                    tank['Tank_index'], tank['Controller_polling_address'], mac_address, tank['Display_unit'], tank['UOM'], tank['Capacity'], tank['Name'], str(site_device), tank['Product']) for tank in tank_details]
                for future in as_completed(tank_details_futures):
                    data.extend(future.result())
            return utils.CustomResponse.Success(data)
        else:
            return utils.CustomResponse.Failure('No site passed')


# class RevampedCurrentTankDetails(APIView):
#     permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
#                            permissions.IsActiveAuthenticatedProductAdmin |
#                            permissions.IsCompanyAdmin |
#                            permissions.TankPermission)]
#     serializer = LatestTankLogSerializer

#     def get(self, request, *args, **kwargs):
#         _site_ids = request.GET.get('site_ids')

#         if _site_ids is None:
#             return utils.UpdatedCustomResponse.Failure("Site Id not passed", status=400)

#         site_ids = [int(x) for x in _site_ids.split(',')]

#         all_data = get_dashboard(site_ids)
#         rpt = []
#         for i in all_data:
#             fill = round((float(i['pv']) / float(i['Capacity'])) * 100, 2)

#             record = OrderedDict([
#                 ('Tank_name', i['tank_name']),
#                 ('Volume', i['pv']),
#                 ('last_updated_time', i['last_updated_time']),
#                 ('Site_id', i['site_id']),
#                 ('siteName', i['site_name']),
#                 ('Capacity', i['Capacity']),
#                 ('DisplayUnit', i['Display_unit']),
#                 ('Product', i['product_name']),
#                 ('Fill', fill),
#                 ('temperature', i['temperature']),
#                 ('water', i['water']),
#                 ('Tank_Status', i['status']),
#             ])

#             rpt.append(record)

#         return utils.UpdatedCustomResponse.Success(rpt, status=status.HTTP_200_OK)


class RevampedCurrentTankDetails(APIView):
    permission_classes = [
        permissions.IsActiveAuthenticatedSuperAdminGroup |
        permissions.IsActiveAuthenticatedProductAdmin |
        permissions.IsCompanyAdmin |
        permissions.TankPermission
    ]
    serializer_class = LatestTankLogSerializer

    def _get_sites_with_tanks(self, site_ids):
        """
        Fetches sites, their devices (MAC addresses), and tanks.
        Returns a list of dicts: [{site_id, mac_address, tanks}], or a failure response.
        """
        sites = models.Sites.objects.filter(
            Site_id__in=site_ids).select_related("Device")
        if not sites.exists():
            return None, utils.UpdatedCustomResponse.Failure(
                "No sites found for given site(s)",
                status=status.HTTP_404_NOT_FOUND
            )

        site_data = []
        for site in sites:
            mac_address = getattr(site.Device, "Device_unique_address", None)
            if not mac_address:
                return None, utils.UpdatedCustomResponse.Failure(
                    f"Device MAC address not found for site {site.Site_id}",
                    status=status.HTTP_404_NOT_FOUND
                )

            tanks = models.Tanks.objects.filter(
                Site_id=site.Site_id, Status=True).values()
            if not tanks.exists():
                return None, utils.UpdatedCustomResponse.Failure(
                    f"No tanks found for site {site.Site_id}",
                    status=status.HTTP_404_NOT_FOUND
                )

            site_data.append({
                "site_id": site.Site_id,
                "site_name": site.Name,
                "mac_address": mac_address,
                "tanks": list(tanks)
            })

        return site_data, None

    def _get_tank_status(self, last_updated) -> str:
        """
        Classify tank status based on last_updated datetime or string.
        """
        if not last_updated:
            return "inactive"

        # If last_updated is a string → parse to datetime
        if isinstance(last_updated, str):
            try:
                last_updated = parser.parse(last_updated)
            except Exception:
                return "inactive"

        # Use UTC-aware now if last_updated has tzinfo
        now = datetime.datetime.now(
            datetime.timezone.utc) if last_updated.tzinfo else datetime.datetime.utcnow()
        diff_hours = (now - last_updated).total_seconds() / 3600

        if diff_hours <= 2:
            return "active"
        elif 2 < diff_hours < 48:
            return "offline"
        return "inactive"

    def _build_tank_record(self, mac_address, tank, site_name, product_name):
        """
        Builds a structured OrderedDict for a single tank.
        """
        tank_log = get_latest_tank_log(
            mac_address,
            tank['Tank_index'],
            tank['Controller_polling_address'],
            tank['Tank_controller']
        )
        fill = round((float(tank_log.get('pv', 0)) /
                     float(tank['Capacity'])) * 100, 2)
        status = self._get_tank_status(
            tank_log.get('read_at', ''))
        convert_capacity_to_ltrs = to_liters(
            tank.get('Capacity', 0), tank['UOM'])
        convert_volume_to_ltrs = to_liters(tank_log.get('pv', 0), tank['UOM'])
        return OrderedDict([
            ('siteName', site_name),
            ('Tank_name', tank['Name']),
            ('Volume', convert_volume_to_ltrs),
            ('Site_id', tank['Site_id']),
            ('Capacity', convert_capacity_to_ltrs),
            ('DisplayUnit', tank['Display_unit']),
            ('temperature', tank_log.get('temperature', 0)),
            ('water', tank_log.get('water', 0)),
            ('Tank_Status', status),
            ('Product', product_name),
            ('Fill', fill),
            ('last_updated_time', tank_log.get('read_at', '')),
        ])

    def get(self, request, *args, **kwargs):
        site_ids_param = request.query_params.get('site_ids')
        if not site_ids_param:
            return utils.UpdatedCustomResponse.Failure(
                "Missing required parameter: site",
                status=status.HTTP_400_BAD_REQUEST
            )

        site_ids = [s.strip() for s in site_ids_param.split(',') if s.strip()]
        if not site_ids:
            return utils.UpdatedCustomResponse.Failure(
                "No valid site IDs provided",
                status=status.HTTP_400_BAD_REQUEST
            )

        site_data, error = self._get_sites_with_tanks(site_ids)
        if error:
            return error

        report = []
        for site in site_data:
            mac_address = site["mac_address"]
            for tank in site["tanks"]:
                product_name = models.Products.objects.get(
                    Product_id=tank['Product_id']).Name
                report.append(self._build_tank_record(
                    mac_address, tank, site["site_name"], product_name))

        return utils.UpdatedCustomResponse.Success(report, status=status.HTTP_200_OK)


class PreviousCurrentTankDetails(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        site_ids = request.data.get('site', None)
        if site_ids:
            # get tank_ids from the sites required
            tank_ids = list(models.Sites.objects.prefetch_related(
                'tanks').filter(pk__in=site_ids).values_list('tanks', flat=True))
            data = []
            # use threading to get the logs for each tank
            with ThreadPoolExecutor() as executor:
                tank_details_futures = [executor.submit(
                    q.get_tank_latest_log,
                    tank_id) for tank_id in tank_ids]
                for future in as_completed(tank_details_futures):
                    data.extend(future.result())
            # update data
            data = log_utils.update_tankgroup_records(data)
            return utils.CustomResponse.Success(data)
        else:
            return utils.CustomResponse.Failure('No site passed')


class CurrentTankDetails(APIView):
    authentication_classes = ()
    permission_classes = ()
    serializer = LatestTankLogSerializer

    def post(self, request, *args, **kwargs):
        site_ids = request.data.get('site', None)
        if site_ids:
            # tank_ids = list(models.Sites.objects.prefetch_related('tanks').filter(pk__in=site_ids).values_list('tanks', flat=True))
            returned = LatestTankLogSerializer(
                models.LatestAtgLog.objects.filter(Site_id__in=site_ids), many=True)
            return utils.CustomResponse.Success(returned.data, status=status.HTTP_200_OK)
        else:
            return utils.CustomResponse.Failure('No site passed')


class CurrentTankDetailsForTankgroup(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        tankgroup_ids = request.data.get('tankgroups', None)
        final_data = []
        for tgID in tankgroup_ids:
            try:
                tg = models.TankGroups.objects.get(pk=tgID)
            except models.TankGroups.DoesNotExist:
                continue

            tg_name = tg.Name
            tg_capacity = tg.current_capacity
            tg_product = tg.Product.Name
            tg_tank_count = tg.tank_count
            tg_volume = 0
            tg_last_update_time = ""
            tg_fill = 0

            tank_ids = list(tg.Tanks.values_list('Tank_id', flat=True))
            data = []
            # use threading to get the logs for each tank
            with ThreadPoolExecutor() as executor:
                tank_details_futures = [executor.submit(
                    q.get_tankgroup_tank_latest_log,
                    tank_id) for tank_id in tank_ids]
                for future in as_completed(tank_details_futures):
                    data.extend(future.result())
            # update data
            tank_data = log_utils.update_tankgroup_records(data)
            if tank_data:
                tg_volume = sum(float(tank['tankVolume'])
                                for tank in tank_data)
                tg_fill = round((float(tg_volume) * 100 / tg_capacity), 2)
                tg_last_update_time = max(datetime.datetime.strptime(
                    tank['last_updated_time'], '%Y-%m-%d %H:%M:%S') for tank in tank_data)
                tg_last_update_time = tg_last_update_time.strftime(
                    '%Y-%m-%d %H:%M:%S')

            tankgroup_data = {
                "Name": tg_name,
                "Capacity": tg_capacity,
                "Volume": tg_volume,
                "Product": tg_product,
                "Tank_count": tg_tank_count,
                "Fill(%)": tg_fill,
                "last_updated_time": tg_last_update_time
            }

            payload = {
                "accumulated": tankgroup_data,
                "tanks": tank_data
            }
            final_data.append(payload)
        return utils.CustomResponse.Success(final_data)


class RevampedCurrentTankDetailsForTankgroup(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        tankgroup_ids = request.data.get('tankgroups', None)
        final_data = []
        for tgID in tankgroup_ids:
            try:
                tg = models.TankGroups.objects.get(pk=tgID)
            except models.TankGroups.DoesNotExist:
                continue

            tg_name = tg.Name
            tg_capacity = tg.current_capacity
            tg_product = tg.Product.Name
            tg_tank_count = tg.tank_count
            tg_volume = 0
            tg_last_update_time = tg.Updated_at
            tg_fill = 0

            tank_ids = list(tg.Tanks.values_list('Tank_id', flat=True))
            data = []
            # use threading to get the logs for each tank
            with ThreadPoolExecutor() as executor:
                revampedtank_details_futures = [executor.submit(
                    q.revamped_get_tankgroup_tank_latest_log, tank_id) for tank_id in tank_ids]
                for future in as_completed(revampedtank_details_futures):
                    print(future.result())
                    data.extend(future.result())

            tank_data = data
            if tank_data:
                try:
                    tg_volume = sum(float(tank['Volume']) for tank in data)
                    tg_fill = round((float(tg_volume) * 100 / tg_capacity), 2)
                    tg_last_update_time = max(datetime.datetime.strptime(
                        f"{tank['last_updated_time']}", '%Y-%m-%d %H:%M:%S') for tank in tank_data)
                    tg_last_update_time = tg_last_update_time.strftime(
                        '%Y-%m-%d %H:%M:%S')
                except:
                    pass
            tankgroup_data = {
                "Name": tg_name,
                "Capacity": tg_capacity,
                "Volume": tg_volume,
                "Product": tg_product,
                "Tank_count": tg_tank_count,
                "Fill(%)": tg_fill,
                "last_updated_time": tg_last_update_time
            }

            payload = {
                "accumulated": tankgroup_data,
                "tanks": tank_data
            }
            final_data.append(payload)
        return utils.CustomResponse.Success(final_data)


# class TankMap(APIView):
#     permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup | permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.TankPermission | permissions.TankGroupsPermission)]

#     serializer_class = TankMapSerializer

#     def post(self, request, *args, **kwargs):
#         serialized = self.serializer_class(data=request.data)
#         if serialized.is_valid():
#             serialized_data = serialized.validated_data
#             site_id = serialized_data['site_ids']

#             if site_id:
#                 result = log_utils.getSitesAndTanks(site_id)
#                 return utils.CustomResponse.Success(result)
#             else:
#                 return utils.CustomResponse.Failure('No site passed')
class TankMap(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup | permissions.IsActiveAuthenticatedProductAdmin |
                           permissions.IsCompanyAdmin | permissions.TankPermission | permissions.TankGroupsPermission)]
    serializer_class = TankMapSerializer

    def get(self, request, site_ids, *args, **kwargs):
        if site_ids:
            result = log_utils.getSitesAndTanks(site_ids)
            return utils.UpdatedCustomResponse.Success(result)
        else:
            return utils.UpdatedCustomResponse.Failure('No site passed')


class AtgTlsConfig(APIView):
    permission_classes = ()
    authentication_classes = ()

    def get(self, request, *args, **kwargs):
        res = []
        mac_address = request.GET['mac_address']
        device = models.Devices.objects.filter(
            Device_unique_address=mac_address).values('Device_id')
        print(device)
        if len(device) <= 0:
            return utils.CustomResponse.Failure("Device Not Found", status=404)
        try:
            site = models.Sites.objects.filter(
                Device=device[0]['Device_id']).values('Site_id')
        except:
            return utils.CustomResponse.Failure(error="No Site Record Found for the Device Provided", status=404)
        tanks = models.Tanks.objects.filter(Site=site[0]['Site_id'], Tank_controller='TLS').values(
            'Tank_id', 'Tank_index', 'Controller_polling_address')
        tank_count = len(tanks)
        for tank in tanks:
            res.append({"tank_id": tank['Tank_id'], "tank_index": tank['Tank_index'],
                       "pol_addr": tank['Controller_polling_address']})
        data = {"ATG_type": "TLS", "Tank_count": tank_count, "tank_data": res}
        return utils.CustomResponse.Success(data)


class AtgTlsLogs(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        logs = request.data
        duplicate_log = 0
        saved_log = 0
        for log in logs:
            mac_address = log[0]
            tank_index = log[1]
            pol_addr = log[2]
            read_at = log[3]
            volume = log[4]
            height = log[5]
            water = log[6]
            temperature = log[7]
            controller_type = log[8]
            tank_id = log[9]
            transaction_id = log[10]
            if models.AtgPrimaryLog.objects.filter(transaction_id=transaction_id).exists():
                duplicate_log += 1
                continue
            else:
                models.AtgPrimaryLog.objects.create(
                    device_address=mac_address,
                    tank_index=tank_index,
                    read_at=read_at,
                    pv=volume,
                    sv=height,
                    controller_type=controller_type,
                    tank_id=tank_id,
                    transaction_id=transaction_id,
                    pv_flag=2,
                    water=water,
                    temperature=temperature,
                    multicont_polling_address=pol_addr
                )
                # update latest Atglog table on the background
                background_thread = threading.Thread(
                    target=update_pic_tls_latest_atg_log, args=(log,))
                # Start the thread
                background_thread.start()
                saved_log += 1
        return utils.CustomResponse.Success(f"{duplicate_log} Duplicate logs(s) found, {saved_log} log(s) saved successfully", status=status.HTTP_200_OK)

    def get(self, request):
        transaction_id = request.GET['transaction_id']
        if models.AtgPrimaryLog.objects.filter(transaction_id=transaction_id).exists():
            return utils.CustomResponse.Success('y', status=status.HTTP_200_OK)
        return utils.CustomResponse.Success('n', status=status.HTTP_200_OK)


class PicTlsDeliveryLogger(APIView):
    '''
    TLS devices attempt to store multiple deliveries to the DB.
    '''
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        # logs are tuple of tuples; format direct from device sqlite cursor
        logs = request.data
        saved = 0
        duplicate = 0
        for log in logs:
            mac_address = log[0]
            tank_index = log[1]
            pol_addr = log[2]
            start_time = log[3]
            end_time = log[4]
            start_vol = log[5]
            end_vol = log[6]
            start_height = log[7]
            end_height = log[8]
            controller_type = log[9]
            tank_id = log[10]
            transaction_id = log[11]
            vol = float(end_vol) - float(start_vol)
            check_if_exist = models.Deliveries.objects.filter(
                transaction_id=transaction_id).values()
            if len(check_if_exist) > 0:
                duplicate += 1
                continue
            else:
                try:
                    models.Deliveries.objects.create(
                        transaction_id=transaction_id,
                        device_address=mac_address,
                        tank_index=tank_index,
                        system_start_time=start_time,
                        system_end_time=end_time,
                        start_height=start_height,
                        end_height=end_height,
                        start_volume=start_vol,
                        end_volume=end_vol,
                        controller_type=controller_type,
                        volume=vol,
                        polling_address=pol_addr,
                        tank_id=tank_id
                    )
                    saved += 1
                except:
                    duplicate += 1
                    continue
        return utils.CustomResponse.Success(f"{duplicate} duplicate log(s) found! and {saved} log(s) saved Successfully")

    def get(self, request):
        transaction_id = request.GET['transaction_id']
        if models.Deliveries.objects.filter(transaction_id=transaction_id).exists():
            return utils.CustomResponse.Success('y', status=status.HTTP_200_OK)
        return utils.CustomResponse.Success('n', status=status.HTTP_200_OK)


class SensorChartConfig(APIView):
    '''
    sensor_data is in the format
    (local_id,controller_address,tank_index,current,voltage,device_address,controller_type,read_at)
    '''
    permission_classes = ()
    authentication_classes = ()

    def get(self, request, *args, **kwargs):
        tank_id = request.GET['tank_id']
        tank_chart = []
        tank = models.Tanks.objects.get(Tank_id=tank_id)
        try:
            data = get_tank_calibration_chart(tank)
            for chart_item in data:
                tank_chart.append(
                    {
                        "Height(mm)": int(chart_item['Height(mm)']),
                        "Volume(ltrs)": int(chart_item['Volume(ltrs)'])
                    }
                )
            result = {"offset": tank.Offset, "tank_chart": tank_chart}
            return utils.CustomResponse.Success(result)
        except:
            return utils.CustomResponse.Failure(f'Failed: No tank chart was found for this ID {tank_id}')


"""
    HELIOS CONSOLE DATA FORMAT IMPLEMENTATION
"""


class AtgHlsConfig(APIView):
    """
        get remote config for ATG HLS Devices
    """
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        # find the device with the
        if request.GET.get('device_address') == None:
            return utils.CustomResponse.Failure(error="No Device Address Passed")

        # check through the device for the Device
        try:
            device_instance = models.Devices.objects.get(
                Device_unique_address=request.GET.get('device_address'))
        except models.Devices.DoesNotExist:
            return utils.CustomResponse.Failure(error="Device Not Found", status=status.HTTP_404_NOT_FOUND)

        tanks = models.Tanks.objects.filter(Tank_controller='HLS')
        result = []
        for tank in tanks:
            data = {
                "Tank_id": tank.Tank_id,
                "Tank_index": tank.Tank_index,
                "Pol_Addr": tank.Controller_polling_address
            }
            result.append(data)
        """
            Construct Response from the tanks record 
        """
        response = {
            "ATG_type": "HLS",
            "tank_count": tanks.count(),
            "tank_data": result
        }

        return utils.CustomResponse.Success(data=response, status=status.HTTP_200_OK)


class AtgHlsLogs(APIView):
    """
        Collect Logs from ATG HLS Device
        Log Format: 
        [["Pic-00001", 1, 1, "2000-01-01 00:00:30", "7231.585", "532.9361", "81.72277", "28.86896","HLS",102,"Pic-00001000101000030102"]]
        ["MAC_Address", "Tank_Index", "Pol_Addr", "Date_Time", "PV_Volume", "SV_Height","Water", "Temperature", "ATG_Type", "Tank_ID", "Unique_ID"]
    """
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        if type(request.data) != list:
            return utils.CustomResponse.Failure("Invalid Log Format", status=400)

        if len(request.data) == 0:
            return utils.CustomResponse.Failure(error="Empty Log Sent", status=400)

        main_log = request.data[0]
        if len(request.data[0]) < 11:
            return utils.CustomResponse.Failure(error="Incomplete Log Sent", status=400)

        mac_address = main_log[0]
        tank_index = main_log[1]
        polling_address = main_log[2]
        date_time = main_log[3]
        pv_volume = main_log[4]
        sv_height = main_log[5]
        water = main_log[6]
        temperature = main_log[7]
        atg_type = main_log[8]
        tank_id = main_log[9]
        unique_id = main_log[10]

        # check if the logs exist
        log_exist = models.AtgPrimaryLog.objects.filter(
            transaction_id=unique_id).exists()
        if log_exist == True:
            return utils.CustomResponse.Failure(error="Log Already Exists", status=status.HTTP_400_BAD_REQUEST)
        # check if the device exists
        try:
            device = models.Devices.objects.get(
                Device_unique_address=mac_address)
        except models.Devices.DoesNotExist:
            return utils.CustomResponse.Failure(error="device not found", status=status.HTTP_404_NOT_FOUND)

        # check if the tank exists
        try:
            tank = models.Tanks.objects.get(Tank_id=tank_id)
        except models.Tanks.DoesNotExist:
            return utils.CustomResponse.Failure(error="tank details not found", status=status.HTTP_404_NOT_FOUND)

        # Calculate pv log
        # get the last log for that tank
        last_log = models.AtgPrimaryLog.objects.filter(
            controller_type=atg_type, device_address=device.Device_unique_address, tank_id=tank.Tank_id).order_by('-read_at')

        # if no last log then allow it pass
        if len(last_log) == 0:
            last_log_volume = pv_volume
        else:
            last_log_volume = last_log[0].pv

        if last_log_volume == pv_volume:
            pv_flag = 1
        elif last_log_volume > pv_volume:
            pv_flag = 2
        else:
            pv_flag = 3

        log_record = models.AtgPrimaryLog.objects.create(
            device_address=mac_address,
            tank_index=tank_index,
            probe_address=polling_address,
            read_at=date_time,
            sv=sv_height,
            controller_type=atg_type,
            tank_id=tank_id,
            transaction_id=unique_id,
            pv=pv_volume,
            pv_flag=pv_flag,
            multicont_polling_address=polling_address,
            water=water,
            temperature=temperature
        )
        log_record.save()
        # Construct log to use existing implementation
        '''
            data[0]: device_address
            data[1]: multicont_polling_address
            data[2]: tank_index
            data[3]: read_at
            data[4]: pv
            data[5]: sv
            data[6]: controller_type
            data[7]: tank_id
            data[8]: transaction_id 
        '''
        data = [mac_address, polling_address, tank_index, date_time,
                pv_volume, sv_height, atg_type, tank_id, unique_id]
        # update last seen
        update_device_online_status_for_PicAtg(data)
        # update latest Atglog table
        update_pic_latest_atg_log(data)
        return utils.CustomResponse.Success(data="Log(s) Created", status=status.HTTP_201_CREATED)


class ConfirmAtgHlsLogs(APIView):
    """
        Confirm Logs on the DB 
    """
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        # check the DB if the data exist with the same logs
        if request.GET.get('transaction_id') == None:
            return utils.UpdatedCustomResponse.Failure(errors="no transaction ID passed")
        else:
            log_exist = models.AtgPrimaryLog.objects.filter(
                transaction_id=request.GET.get('transaction_id')).exists()
            if log_exist:
                return Response("y", status=status.HTTP_200_OK)
        return Response("n", status=status.HTTP_400_BAD_REQUEST)


"""
   END HELIOS CONSOLE DATA FORMAT IMPLEMENTATION
"""
